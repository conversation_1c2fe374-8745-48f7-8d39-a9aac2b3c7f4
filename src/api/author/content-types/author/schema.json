{"kind": "collectionType", "collectionName": "authors", "info": {"singularName": "author", "pluralName": "authors", "displayName": "Author"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "mappedBy": "author"}}}