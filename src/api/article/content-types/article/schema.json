{"kind": "collectionType", "collectionName": "articles", "info": {"singularName": "article", "pluralName": "articles", "displayName": "Article"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "excerpt": {"type": "text"}, "content": {"type": "richtext", "required": true}, "coverImage": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images", "files", "videos", "audios"]}, "isFeatured": {"type": "boolean", "default": false}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::category.category", "inversedBy": "articles"}, "tags": {"type": "relation", "relation": "manyToMany", "target": "api::tag.tag", "inversedBy": "articles"}, "author": {"type": "relation", "relation": "manyToOne", "target": "api::author.author", "inversedBy": "articles"}, "seo": {"type": "component", "component": "shared.seo", "repeatable": false, "required": true}}}