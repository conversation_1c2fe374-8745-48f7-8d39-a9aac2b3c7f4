{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "text"}, "articles": {"type": "relation", "relation": "manyToMany", "target": "api::article.article", "mappedBy": "categories"}}}