{"kind": "collectionType", "collectionName": "tags", "info": {"singularName": "tag", "pluralName": "tags", "displayName": "Tag"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "articles": {"type": "relation", "relation": "manyToMany", "target": "api::article.article", "mappedBy": "tags"}}}