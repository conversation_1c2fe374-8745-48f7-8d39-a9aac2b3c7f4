name: Strapi CMS Build and Deploy

on:
  push:
    branches: ["master", "main"]

env:
  REGISTRY: r-hub.thecapsai.com
  IMAGE_NAME: chatbot-strapi-cms
  DOCKER_COMPOSE_DIR: /home/<USER>/saas-ai-chatbot-infra

jobs:
  build_and_push:
    name: Build and Push Strapi CMS Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log into registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            NODE_ENV=production
            STRAPI_URL=https://pv-cms.thecapsai.com
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    name: Deploy Strapi CMS to Server
    runs-on: ubuntu-latest
    needs: build_and_push
    if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main'

    steps:
      - name: Deploy to server via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.KEY }}
          script: |
            cd ${{ env.DOCKER_COMPOSE_DIR }}

            echo "🔄 Pulling latest Strapi CMS image..."
            docker compose pull chatbot-strapi

            echo "🛑 Stopping old containers..."
            docker compose stop chatbot-strapi || true

            echo "🗑️ Removing old containers..."
            docker compose rm -f chatbot-strapi || true

            echo "🚀 Starting new containers..."
            docker compose up -d chatbot-strapi

            echo "🧹 Cleaning up unused images..."
            docker image prune -a -f

            echo "✅ Strapi deployment completed!"
