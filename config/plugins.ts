export default ({ env }) => ({
    seo: {
        enabled: true,
    },
    upload: {
        config: {
            provider: 'aws-s3',
            providerOptions: {
                s3Options: {
                    credentials: {
                        accessKeyId: env('STRAPI_AWS_ACCESS_KEY_ID'),
                        secretAccessKey: env('STRAPI_AWS_ACCESS_SECRET'),
                    },
                    region: env('STRAPI_AWS_REGION'),
                    params: {
                        ACL: env('AWS_ACL', 'public-read'),
                        signedUrlExpires: env('AWS_SIGNED_URL_EXPIRES', 15 * 60),
                        Bucket: env('STRAPI_AWS_BUCKET'),
                    },
                },
            },
            actionOptions: {
                upload: {},
                uploadStream: {},
                delete: {},
            },
        },
    },
});
