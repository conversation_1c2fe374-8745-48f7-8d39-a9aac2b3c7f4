#!/bin/bash

# Exit on any error
set -e

# Configuration
REGISTRY="r-hub.thecapsai.com"
IMAGE_NAME="chatbot-strapi-cms"
TAG="latest"
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${TAG}"

echo "🚀 Building and pushing Docker image..."
echo "Registry: ${REGISTRY}"
echo "Image: ${IMAGE_NAME}"
echo "Tag: ${TAG}"
echo "Full image name: ${FULL_IMAGE_NAME}"
echo ""

# Build the Docker image
echo "📦 Building Docker image..."
docker build --platform=linux/amd64 \
  --build-arg NODE_ENV=production \
  --build-arg STRAPI_URL=https://pv-cms.thecapsai.com \
  -t ${FULL_IMAGE_NAME} .

# Push the image to the registry
echo "📤 Pushing image to registry..."
docker push ${FULL_IMAGE_NAME}

echo ""
echo "✅ Successfully built and pushed ${FULL_IMAGE_NAME}"
echo "🎉 Deployment complete!" 